import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import 'package:milestone/models/home_platform.dart';
import 'package:milestone/models/product_list.dart';
import 'package:milestone/utils/user_utils.dart';

import '../models/bind_invite_code_response.dart';
import '../models/brand_product_filter.dart';
import '../models/category.dart';
import '../models/collected_product_list_response.dart';
import '../models/create_recharge_response.dart';
import '../models/invitee_info.dart';
import '../models/order_list_response.dart';
import '../models/payment_query_response.dart';
import '../models/payment_response.dart';
import '../models/product_info.dart';
import '../models/recharge_config.dart';
import '../models/revenue_info.dart';
import '../models/share_link_product_info.dart';
import '../models/team_summary.dart';
import '../models/transaction_detail_model.dart';
import '../models/user_info.dart';
import 'errors.dart';
import 'package:http/http.dart' as http;

NetworkApiClient networkApiClient = NetworkApiClient();

class NetworkApiClient {
  final String baseHomeUrl =
      "https://api.gencobse.com"; //"https://api.genconusantara.com";
  late Dio apiClient;

  NetworkApiClient() {
    apiClient = Dio()
      ..options.baseUrl = baseHomeUrl
      ..options.contentType = Headers.formUrlEncodedContentType
      ..options.connectTimeout = const Duration(seconds: 20)
      ..options.receiveTimeout = const Duration(seconds: 20)
      ..options.sendTimeout = const Duration(seconds: 20)
      ..options.headers = {"Accept-Language": "zh"};

    if (kDebugMode) {
      apiClient.interceptors.add(
        LogInterceptor(responseBody: true, requestBody: true),
      );
    }
  }

  Future<Response> mobileLogin({
    required String phone,
    required String captcha,
    int spreadSpid = 0,
  }) async {
    return apiClient.post(
      '/api/front/login/mobile',
      data: {"phone": phone, "captcha": captcha, "spread_spid": spreadSpid},
      options: Options(contentType: Headers.jsonContentType),
    );
  }

  Future<Response> accountLogin({
    required String account,
    required String password,
    int spreadSpid = 0,
  }) async {
    return apiClient.post(
      '/api/front/login',
      data: {
        "account": account,
        "password": password,
        "spread_spid": spreadSpid,
      },
      options: Options(contentType: Headers.jsonContentType),
    );
  }

  Future<Response> thirdPartyLoginLogin({
    required String code,
    required String codeVerifier,
    int spreadSpid = 0,
  }) async {
    String platform = Platform.isAndroid ? "android" : "ios";
    return apiClient.post(
      '/api/front/authorize/login',
      data: {
        "code": code,
        "code_verifier": codeVerifier,
        "spread_spid": spreadSpid,
      },
      options: Options(
        headers: {"platform": platform},
      ),
    );
  }

  Future<BindInviteCodeResponse> bindInviteCode({
    required String inviteCode,
  }) async {
    Response response = await apiClient.post(
      '/api/front/user/bindInviteCode',
      queryParameters: {"inviteCode": inviteCode},
      options: defaultLoginOptions(),
    );
    return parseResponseFromJson(response, BindInviteCodeResponse.fromJson);
  }

  Future<void> logout() async {
    Response response = await apiClient.get(
      '/api/front/logout',
      options: defaultLoginOptions(),
    );
    parseResponse(response);
  }

  Future<Response> sendVerificationCode(String phone) async {
    return apiClient.post(
      '/api/front/sendCode',
      queryParameters: {"phone": phone},
    );
  }

  Future<Response> resetPassword({
    required String account,
    required String password,
    required String captcha,
  }) async {
    return apiClient.post(
      '/api/front/register/reset',
      data: {"account": account, "password": password, "captcha": captcha},
      options: defaultLoginOptions(),
    );
  }

  // https://shop.tiktok.com/view/product/1729714831816951615
  Future<ProductResponse> searchLink({required String link}) async {
    Response response = await apiClient.post(
      '/api/front/product/search',
      data: {"url": link},
      options: defaultLoginOptions(),
    );
    Map<String, dynamic> result = parseResponse(response);
    Logger().d("result:$result");
    ProductResponse productResponse = ProductResponse.fromJson(result);
    return productResponse;
  }

  Future<ProductResponse> getProductDetail(int productId) async {
    Response response = await apiClient.get(
      '/api/front/product/detail/$productId',
      // queryParameters: {"id": productId},
      // options: defaultLoginOptions(),
    );
    Map<String, dynamic> result = parseResponse(response);
    ProductResponse productResponse = ProductResponse.fromJson(result);
    return productResponse;
  }

  Future<ShareLinkProductInfo> getProductShareInfo(int productId) async {
    Response response = await apiClient.get(
      '/api/front/product/share/$productId',
      options: defaultLoginOptions(),
    );
    Map<String, dynamic> result = parseResponse(response);
    ShareLinkProductInfo productInfo = ShareLinkProductInfo.fromJson(result);
    return productInfo;
  }

  Map<String, dynamic> parseResponse(Response response) {
    final Map<String, dynamic> responseBody = response.data is Map
        ? response.data as Map<String, dynamic>
        : json.decode(response.data);

    int code = responseBody['code'];
    String message = responseBody['message'] ?? '';

    if (code == 200) {
      return responseBody["data"] ?? {};
    }
    throw ApiException(code, message);
  }

  T parseResponseFromJson<T>(
    Response response,
    T Function(Map<String, dynamic>) fromJson,
  ) {
    final Map<String, dynamic> responseBody = response.data is Map
        ? response.data as Map<String, dynamic>
        : json.decode(response.data);

    final int code = responseBody['code'];
    final String message = responseBody['message'] ?? '';

    if (code == 200) {
      // 解析 data 字段并转换为指定类型
      final dynamic data = responseBody['data'];
      if (data != null && data is Map<String, dynamic>) {
        return fromJson(data);
      } else {
        throw const FormatException('Invalid response data format');
      }
    } else {
      throw ApiException(code, message);
    }
  }

  Options defaultLoginOptions() {
    String token = UserManager.getToken();
    Logger().d("token:$token");
    return Options(
      contentType: Headers.jsonContentType,
      headers: {"Authori-zation": token},
    );
  }

  Future<ProductListResponse> getHotProducts(
    HomePlatform homePlatform, {
    int page = 1,
    limit = 10,
  }) async {
    try {
      Response response = await apiClient.get(
        '/api/front/index/product/${homePlatform.type()}',
        queryParameters: {"page": page, "limit": limit},
        options: defaultLoginOptions(),
      );
      return ProductListResponse.fromJson(parseResponse(response));
    } catch (e) {
      rethrow;
    }
  }

  Future<ProductListResponse> getRecommendProducts(
    int productId, {
    int page = 1,
    limit = 10,
  }) async {
    try {
      Response response = await apiClient.get(
        '/api/front/product/recommend/$productId',
        queryParameters: {"page": page, "limit": limit},
        options: defaultLoginOptions(),
      );
      return ProductListResponse.fromJson(parseResponse(response));
    } catch (e) {
      rethrow;
    }
  }

  Future<ProductListResponse> getBrandCategoryProducts(
    code,
    BrandProductFilter filter,
    bool isAsc,
  ) async {
    try {
      Response response = await apiClient.get(
        '/api/front/index/brand/',
        queryParameters: {
          "code": code,
          "orderBy": filter.value(),
          "isAsc": isAsc,
        },
        options: defaultLoginOptions(),
      );
      return ProductListResponse.fromJson(parseResponse(response));
    } catch (e) {
      rethrow;
    }
  }

  Future<ProductListResponse> getGoodProducts() async {
    try {
      Response response = await apiClient.get(
        '/api/front/product/good',
        options: defaultLoginOptions(),
      );
      return ProductListResponse.fromJson(parseResponse(response));
    } catch (e) {
      rethrow;
    }
  }

  Future<void> addProductToFavorites(int productId, String category) async {
    Response response = await apiClient.post(
      '/api/front/collect/add',
      data: {"id": productId, "category": category},
      options: defaultLoginOptions(),
    );
    parseResponse(response);
    Logger().d("response:$response");
  }

  Future<void> removeProductFromFavorites(int productId) async {
    Response response = await apiClient.post(
      '/api/front/collect/cancel/$productId',
      data: {"productId": productId},
      options: defaultLoginOptions(),
    );
    parseResponse(response);
    Logger().d("response:$response");
  }

  Future<ProductCategoryListResponse> getCategoryList(int type) async {
    Response response = await apiClient.get(
      '/api/front/brand/list',
      queryParameters: {"type": type},
      options: defaultLoginOptions(),
    );
    Map<String, dynamic> result = parseResponse(response);
    Logger().d("result: $result");
    return ProductCategoryListResponse.fromJson(result);
  }

  Future<void> extractCash(
    String name,
    String extractType,
    String bankCode,
    String bankName,
    String amount,
    String mark,
  ) async {
    Response response = await apiClient.post(
      "/api/front/extract/cash",
      data: {
        "name": name,
        "extractType": extractType,
        "cardum": bankCode,
        "bankName": bankName,
        "amount": amount,
        "mark": mark,
      },
      options: defaultLoginOptions(),
    );
    parseResponse(response);
  }

  Future<RevenueInfo> getRevenueInfo() async {
    Response response = await apiClient.get(
      '/api/front/user/revenue',
      options: defaultLoginOptions(),
    );
    Map<String, dynamic> result = parseResponse(response);
    return RevenueInfo.fromJson(result);
  }

  // type : 记录类型：all-全部，expenditure-支出，income-收入
  Future<TransactionDetailModel> getTransactionDetailList({
    int page = 1,
    int limit = 20,
    String type = "all",
  }) async {
    Response response = await apiClient.get(
      '/api/front/recharge/bill/record',
      queryParameters: {'page': page, 'limit': limit, 'type': type},
      options: defaultLoginOptions(),
    );
    Map<String, dynamic> result = parseResponse(response);
    return TransactionDetailModel.fromJson(result);
  }

  Future<CollectedProductListResponse> getCollectedProductList({
    int page = 1,
    int limit = 10,
  }) async {
    Response response = await apiClient.get(
      '/api/front/collect/user',
      queryParameters: {'page': page, 'limit': limit},
      options: defaultLoginOptions(),
    );
    Map<String, dynamic> result = parseResponse(response);
    return CollectedProductListResponse.fromJson(result);
  }

  Future<Map<String, dynamic>> modifyNickname(
    String nickname,
    String avatar,
  ) async {
    Response response = await apiClient.post(
      '/api/front/user/edit',
      data: {"nickname": nickname, "avatar": avatar},
      options: defaultLoginOptions(),
    );
    return parseResponse(response);
  }

  Future<Map<String, dynamic>> uploadAvatar(String filePath) async {
    FormData formData = FormData.fromMap({
      "multipart": await MultipartFile.fromFile(
        filePath,
        filename: "avatar.jpg",
      ),
      "model": "user",
      "pid": 0,
    });
    Response response = await apiClient.post(
      '/api/front/image',
      data: formData,
      options: defaultLoginOptions(),
    );
    return parseResponse(response);
  }

  Future<Map<String, dynamic>> getAppInfo(String platform) async {
    Response response = await apiClient.get(
      '/api/front/index/version',
      queryParameters: {"platform": platform},
    );
    return parseResponse(response);
  }

  Future<OrderListResponse> getOrderList({
    int page = 1,
    int limit = 20,
    int? type,
  }) async {
    final response = await apiClient.get(
      '/api/front/order/list',
      queryParameters: {'page': page, 'limit': limit, "type": type},
      options: defaultLoginOptions(),
    );

    final Map<String, dynamic> result = parseResponse(response);
    return OrderListResponse.fromJson(result);
  }

  Future<InviteeInfoResponse> getTeamMembers({
    int grade = 0,
    String? keyword,
    String? sortKey, // 'childCount'/'numberCount'/'orderCount'
    String? isAsc, // 'ASC'/'DESC'
    int page = 1,
    int limit = 20,
  }) async {
    try {
      // 构建查询参数
      Map<String, dynamic> queryParams = {
        'grade': grade.toString(),
        'page': page,
        'limit': limit,
      };

      // 添加可选参数
      if (keyword != null && keyword.isNotEmpty) {
        queryParams['keyword'] = keyword;
      }
      if (sortKey != null) {
        queryParams['sortKey'] = sortKey;
      }
      if (isAsc != null) {
        queryParams['isAsc'] = isAsc;
      }

      // 发送请求
      Response response = await apiClient.get(
        '/api/front/spread/people',
        queryParameters: queryParams,
        options: defaultLoginOptions(),
      );

      // 解析响应
      return InviteeInfoResponse.fromJson(parseResponse(response));
    } catch (e) {
      Logger().e("获取团队成员失败: $e");
      rethrow;
    }
  }

  Future<UserInfo> getUserInfo() async {
    try {
      Response response = await apiClient.get(
        '/api/front/user',
        options: defaultLoginOptions(),
      );

      Map<String, dynamic> result = parseResponse(response);
      return UserInfo.fromJson(result);
    } on DioException catch (e) {
      // 处理Dio特有的异常
      if (e.response != null) {
        throw ApiException(
          e.response!.statusCode ?? 500,
          e.response!.data?['message'] ?? '网络请求异常',
        );
      } else {
        throw ApiException(500, '网络连接异常: ${e.message}');
      }
    } on ApiException {
      rethrow;
    } catch (e) {
      throw ApiException(0, '未知错误: $e');
    }
  }

  Future<CreateRechargeResponse> createRecharge(
    String rechargeType,
    String payType,
    int price,
  ) async {
    Response response = await apiClient.post(
      '/api/front/recharge/create',
      data: {
        "price": price,
        "recharge_id": 0,
        "rechargeType": rechargeType,
        "payType": payType,
      },
      options: defaultLoginOptions(),
    );
    CreateRechargeResponse result = parseResponseFromJson(
      response,
      CreateRechargeResponse.fromJson,
    );
    Logger().d("result:$result");
    return result;
  }

  Future<RechargeConfig> getRechargeConfig() async {
    Response response = await apiClient.get(
      '/api/front/index/recharge/config',
      options: defaultLoginOptions(),
    );
    Map<String, dynamic> result = parseResponse(response);
    return RechargeConfig.fromJson(result);
  }

  Future<PaymentResponse> requestPayment({
    required String uni,
    required String bizType,
    required String orderNo,
    required String payType,
    required String payChannel,
    required String from,
  }) async {
    Response response = await apiClient.post(
      '/api/front/pay/payment',
      data: {
        "uni": uni,
        "bizType": bizType,
        "orderNo": orderNo,
        "payType": payType,
        "payChannel": payChannel,
        "from": from,
      },
      options: defaultLoginOptions(),
    );

    return parseResponseFromJson(response, PaymentResponse.fromJson);
  }

  Future<Map<String, dynamic>> notifyPaymentCallback({
    required String orderNo,
    required String outTradeNo,
    required String amount,
    required bool status,
    required DateTime payTime,
    required String payType,
  }) async {
    final Map<String, dynamic> requestData = {
      "orderNo": orderNo,
      "outTradeNo": outTradeNo,
      "amount": amount,
      "status": status,
      "payTime": payTime.toUtc().toIso8601String(),
      "payType": payType,
    };

    final Response response = await apiClient.post(
      '/api/admin/payment/callback/pay',
      data: requestData,
      options: defaultLoginOptions(),
    );

    Logger().d(response);
    return parseResponse(response);
  }

  Future<PaymentQueryResponse> queryPaymentOrderStatus({
    required String orderNo,
  }) async {
    final Response response = await apiClient.get(
      '/api/front/pay/query',
      queryParameters: {'orderNo': orderNo},
      options: defaultLoginOptions(),
    );
    Logger().d(response);
    return parseResponseFromJson(response, PaymentQueryResponse.fromJson);
  }

  Future<TeamSummary> getTeamSummary() async {
    Response response = await apiClient.get(
      '/api/front/spread/team',
      options: defaultLoginOptions(),
    );

    return parseResponseFromJson(response, TeamSummary.fromJson);
  }
}

/// 获取 URL 的重定向最终地址
///
/// [url] 原始 URL
/// [maxRedirects] 最大重定向次数（防止无限重定向）
///
/// 返回最终 URL 或 null（如果无法解析）
Future<String?> getRedirectedUrl(String url, {int maxRedirects = 1}) async {
  int redirectCount = 0;
  String currentUrl = url;

  while (redirectCount < maxRedirects) {
    final client = http.Client();
    try {
      final request = http.Request('GET', Uri.parse(currentUrl));
      final response = await client.send(request);
      Logger().d("response:$response");

      // 处理重定向
      if (response.statusCode >= 300 && response.statusCode < 400) {
        final location = response.headers['location'];
        if (location != null) {
          // 处理相对路径重定向
          if (location.startsWith('/')) {
            final baseUri = Uri.parse(currentUrl);
            currentUrl = '${baseUri.scheme}://${baseUri.host}$location';
          } else {
            currentUrl = location;
          }
          redirectCount++;
          continue;
        }
      }

      // 非重定向响应，返回最终 URL
      return response.isRedirect ? response.headers['location'] : currentUrl;
    } finally {
      // 正确关闭客户端以释放资源
      client.close();
    }
  }

  return currentUrl;
}
